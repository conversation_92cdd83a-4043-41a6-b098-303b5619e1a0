<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conversation History - Gooda</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>

        .conversation-card {
            transition: all 0.2s ease-in-out;
        }

        .conversation-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>

<body class="bg-gray-50 min-h-screen">
    <!-- Top navigation bar -->
    {% if user %}
    <header class="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3 flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <a href="/" class="text-blue-600 hover:text-blue-800 font-medium">← Back to Chat</a>
                <h1 class="text-xl font-semibold text-gray-900">Conversation History</h1>
            </div>
            <div class="flex items-center space-x-3">
                {% if user.picture %}
                <img src="{{ user.picture }}" alt="{{ user.name or user.email }}" class="h-8 w-8 rounded-full border border-gray-200" />
                {% endif %}
                <span class="text-sm text-gray-700">{{ user.name or user.email }}</span>
                <a href="/logout" class="text-sm text-blue-600 hover:underline">Sign out</a>
            </div>
        </div>
    </header>
    {% endif %}

    <main class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {% if conversations %}
            <div class="space-y-6">
                {% for conversation in conversations %}
                <div class="conversation-card bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <!-- Conversation header -->
                    <div class="px-6 py-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-medium text-gray-900">
                                    Conversation from {{ conversation.started_at.strftime('%B %d, %Y') }}
                                </h3>
                                <p class="text-sm text-gray-500">
                                    Started at {{ conversation.started_at.strftime('%I:%M %p') }} • {{ conversation.messages|length }} messages
                                </p>
                            </div>
                            <div>
                                <a href="/?conversation={{ conversation.conversation_id }}"
                                   class="inline-flex items-center px-3 py-2 border border-blue-300 text-sm font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100 hover:border-blue-400 transition-colors">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                    </svg>
                                    View Full Conversation
                                </a>
                            </div>
                        </div>

                        <!-- First question only -->
                        {% if conversation.messages %}
                        <div class="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                            <p class="text-sm text-blue-600 font-medium mb-2">First question:</p>
                            <p class="text-sm text-gray-800">
                                {{ conversation.messages[0].prompt }}
                            </p>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <!-- Empty state -->
            <div class="text-center py-12">
                <div class="mx-auto h-24 w-24 text-gray-400">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                </div>
                <h3 class="mt-4 text-lg font-medium text-gray-900">No conversations yet</h3>
                <p class="mt-2 text-gray-500">Start a conversation to see your chat history here.</p>
                <div class="mt-6">
                    <a href="/" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Start Chatting
                    </a>
                </div>
            </div>
        {% endif %}
    </main>


</body>

</html>
