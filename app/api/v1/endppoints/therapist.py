from fastapi import <PERSON><PERSON><PERSON><PERSON>, Request, HTTPException

from app.models.prompt_request import PromptRequest
from app.services.therapist import TherapistService
from app.store.messages import MessageStore
from app.auth import get_current_user

router = APIRouter()

@router.post("/therapist")
async def get_therapist_response(p: PromptRequest, request: Request):
    # The middleware already validates authentication, but we can get user info if needed
    user = get_current_user(request)

    # Extract user ID from the authenticated user session
    user_id = user.get("sub") if user else "anonymous"

    service = TherapistService()
    resp = await service.get_therapist_response(p, user_id)
    return resp

@router.get("/conversation/{conversation_id}")
async def get_conversation(conversation_id: str, request: Request):
    # Get current user
    user = get_current_user(request)
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")

    user_id = user.get("sub")

    # Get the specific conversation
    message_store = MessageStore()
    conversation = await message_store.get_conversation_by_id(user_id, conversation_id)

    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")

    return conversation
